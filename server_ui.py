#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证服务器UI管理界面
提供图形化的卡密管理功能

功能特性:
- 卡密添加、删除、启用、禁用
- 用户验证记录查看
- 服务器状态监控
- 批量操作功能

作者: AI Assistant
版本: 1.0
"""

import os
import sys
import sqlite3
import json
import time
import random
import string
import threading
from datetime import datetime, timedelta
from pathlib import Path

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from tkinter.scrolledtext import ScrolledText

class ServerUIManager:
    """服务器UI管理器"""
    
    def __init__(self, db_path='license.db'):
        self.db_path = db_path
        self.window = None
        self.refresh_timer = None
        
        # 数据变量
        self.license_tree = None
        self.log_text = None
        self.stats_labels = {}
        
    def init_window(self):
        """初始化窗口"""
        self.window = tk.Tk()
        self.window.title("验证服务器管理界面 v1.0")
        self.window.geometry("1200x800")
        self.window.configure(bg='#f0f0f0')
        
        # 设置窗口图标（如果有的话）
        try:
            self.window.iconbitmap('icon.ico')
        except:
            pass
        
        self.create_menu()
        self.create_main_interface()
        
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.window)
        self.window.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="刷新数据", command=self.refresh_all_data)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.window.quit)
        
        # 卡密菜单
        license_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="卡密管理", menu=license_menu)
        license_menu.add_command(label="添加卡密", command=self.add_license_dialog)
        license_menu.add_command(label="批量生成", command=self.batch_generate_dialog)
        license_menu.add_separator()
        license_menu.add_command(label="导出卡密", command=self.export_licenses)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def create_main_interface(self):
        """创建主界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建选项卡
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 卡密管理选项卡
        self.create_license_tab(notebook)
        
        # 验证日志选项卡
        self.create_log_tab(notebook)
        
        # 统计信息选项卡
        self.create_stats_tab(notebook)
        
        # 启动定时刷新
        self.start_auto_refresh()
    
    def create_license_tab(self, parent):
        """创建卡密管理选项卡"""
        license_frame = ttk.Frame(parent)
        parent.add(license_frame, text="卡密管理")
        
        # 工具栏
        toolbar = ttk.Frame(license_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(toolbar, text="添加卡密", command=self.add_license_dialog).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="批量生成", command=self.batch_generate_dialog).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="启用", command=self.enable_selected_license).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="禁用", command=self.disable_selected_license).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="删除", command=self.delete_selected_license).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="刷新", command=self.refresh_license_data).pack(side=tk.LEFT, padx=(0, 5))

        # 第二行工具栏 - 批量操作
        toolbar2 = ttk.Frame(license_frame)
        toolbar2.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(toolbar2, text="全选", command=self.select_all_licenses).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar2, text="反选", command=self.invert_selection).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar2, text="批量启用", command=self.batch_enable_licenses).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar2, text="批量禁用", command=self.batch_disable_licenses).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar2, text="批量删除", command=self.batch_delete_licenses).pack(side=tk.LEFT, padx=(0, 5))

        # 选择状态标签
        self.selection_label = ttk.Label(toolbar2, text="未选择", foreground="gray")
        self.selection_label.pack(side=tk.RIGHT)
        
        # 卡密列表
        columns = ('卡密', '项目', '状态', 'HWID', '过期时间', '使用次数', '最后使用', '创建时间')
        self.license_tree = ttk.Treeview(license_frame, columns=columns, show='headings', height=15)

        # 设置列标题和宽度
        column_widths = [180, 100, 60, 120, 100, 80, 100, 120]
        for i, (col, width) in enumerate(zip(columns, column_widths)):
            self.license_tree.heading(col, text=col)
            self.license_tree.column(col, width=width)
        
        # 添加滚动条
        license_scrollbar = ttk.Scrollbar(license_frame, orient=tk.VERTICAL, command=self.license_tree.yview)
        self.license_tree.configure(yscrollcommand=license_scrollbar.set)
        
        # 布局
        self.license_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        license_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件和选择事件
        self.license_tree.bind('<Double-1>', self.edit_license_dialog)
        self.license_tree.bind('<<TreeviewSelect>>', self.on_selection_change)
    
    def create_log_tab(self, parent):
        """创建验证日志选项卡"""
        log_frame = ttk.Frame(parent)
        parent.add(log_frame, text="验证日志")
        
        # 工具栏
        log_toolbar = ttk.Frame(log_frame)
        log_toolbar.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(log_toolbar, text="刷新日志", command=self.refresh_log_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_toolbar, text="清空日志", command=self.clear_logs).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_toolbar, text="导出日志", command=self.export_logs).pack(side=tk.LEFT, padx=(0, 5))
        
        # 日志显示区域
        self.log_text = ScrolledText(log_frame, height=20, font=('Consolas', 10))
        self.log_text.pack(fill=tk.BOTH, expand=True)
    
    def create_stats_tab(self, parent):
        """创建统计信息选项卡"""
        stats_frame = ttk.Frame(parent)
        parent.add(stats_frame, text="统计信息")
        
        # 统计信息显示
        stats_info_frame = ttk.LabelFrame(stats_frame, text="服务器统计", padding=20)
        stats_info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 创建统计标签
        stats_items = [
            ('总卡密数量', 'total_keys'),
            ('有效卡密', 'active_keys'),
            ('已绑定卡密', 'bound_keys'),
            ('总验证次数', 'total_auths'),
            ('成功验证次数', 'success_auths'),
            ('今日验证次数', 'today_auths'),
            ('验证成功率', 'success_rate')
        ]
        
        for i, (label, key) in enumerate(stats_items):
            row = i // 2
            col = i % 2
            
            ttk.Label(stats_info_frame, text=f"{label}:").grid(row=row, column=col*2, sticky=tk.W, padx=(0, 10), pady=5)
            self.stats_labels[key] = ttk.Label(stats_info_frame, text="0", font=('Arial', 12, 'bold'))
            self.stats_labels[key].grid(row=row, column=col*2+1, sticky=tk.W, padx=(0, 30), pady=5)
        
        # 刷新按钮
        ttk.Button(stats_frame, text="刷新统计", command=self.refresh_stats_data).pack(pady=10)
    
    def add_license_dialog(self):
        """添加卡密对话框"""
        dialog = tk.Toplevel(self.window)
        dialog.title("添加卡密")
        dialog.geometry("400x300")
        dialog.transient(self.window)
        dialog.grab_set()
        
        # 居中显示
        dialog.geometry("+%d+%d" % (self.window.winfo_rootx() + 50, self.window.winfo_rooty() + 50))
        
        # 表单
        ttk.Label(dialog, text="项目代码:").pack(pady=5)
        project_var = tk.StringVar(value="DEFAULT")
        project_entry = ttk.Entry(dialog, textvariable=project_var, width=40)
        project_entry.pack(pady=5)

        ttk.Label(dialog, text="卡密 (留空自动生成):").pack(pady=5)
        key_entry = ttk.Entry(dialog, width=40)
        key_entry.pack(pady=5)

        ttk.Label(dialog, text="有效期 (天数):").pack(pady=5)
        expire_var = tk.StringVar(value="30")
        expire_entry = ttk.Entry(dialog, textvariable=expire_var, width=20)
        expire_entry.pack(pady=5)

        ttk.Label(dialog, text="使用次数限制 (-1为无限制):").pack(pady=5)
        uses_var = tk.StringVar(value="-1")
        uses_entry = ttk.Entry(dialog, textvariable=uses_var, width=20)
        uses_entry.pack(pady=5)
        
        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)
        
        def add_license():
            project_code = project_var.get().strip() or "DEFAULT"
            key_code = key_entry.get().strip()
            if not key_code:
                key_code = self.generate_key_code()

            try:
                expire_days = int(expire_var.get())
                max_uses = int(uses_var.get())
            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字")
                return

            if self.add_license(key_code, project_code, expire_days, max_uses):
                messagebox.showinfo("成功", f"卡密添加成功: {key_code}\n项目: {project_code}")
                dialog.destroy()
                self.refresh_license_data()
            else:
                messagebox.showerror("错误", "卡密添加失败")

        # 按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)

        ttk.Button(button_frame, text="添加", command=add_license).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=5)
    
    def batch_generate_dialog(self):
        """批量生成对话框"""
        # 创建批量生成对话框
        dialog = tk.Toplevel(self.window)
        dialog.title("批量生成卡密")
        dialog.geometry("400x250")
        dialog.transient(self.window)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (self.window.winfo_rootx() + 50, self.window.winfo_rooty() + 50))

        # 表单
        ttk.Label(dialog, text="项目代码:").pack(pady=5)
        project_var = tk.StringVar(value="DEFAULT")
        project_entry = ttk.Entry(dialog, textvariable=project_var, width=40)
        project_entry.pack(pady=5)

        ttk.Label(dialog, text="生成数量:").pack(pady=5)
        count_var = tk.StringVar(value="10")
        count_entry = ttk.Entry(dialog, textvariable=count_var, width=20)
        count_entry.pack(pady=5)

        ttk.Label(dialog, text="有效期 (天数):").pack(pady=5)
        expire_var = tk.StringVar(value="30")
        expire_entry = ttk.Entry(dialog, textvariable=expire_var, width=20)
        expire_entry.pack(pady=5)

        def start_batch_generate():
            try:
                project_code = project_var.get().strip() or "DEFAULT"
                count = int(count_var.get())
                expire_days = int(expire_var.get())
            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字")
                return

            if count < 1 or count > 1000:
                messagebox.showerror("错误", "生成数量必须在1-1000之间")
                return

            dialog.destroy()
            self._execute_batch_generate(project_code, count, expire_days)

        # 按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)

        ttk.Button(button_frame, text="开始生成", command=start_batch_generate).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=5)

    def _execute_batch_generate(self, project_code, count, expire_days):
        """执行批量生成"""
        # 生成进度对话框
        progress_dialog = tk.Toplevel(self.window)
        progress_dialog.title("批量生成中...")
        progress_dialog.geometry("300x100")
        progress_dialog.transient(self.window)
        progress_dialog.grab_set()

        progress_var = tk.StringVar(value="正在生成卡密...")
        ttk.Label(progress_dialog, textvariable=progress_var).pack(pady=20)

        progress_bar = ttk.Progressbar(progress_dialog, length=250, mode='determinate')
        progress_bar.pack(pady=10)
        progress_bar['maximum'] = count

        def generate_batch():
            generated_keys = []
            for i in range(count):
                key_code = self.generate_key_code()
                if self.add_license(key_code, project_code, expire_days, -1):
                    generated_keys.append(key_code)

                progress_bar['value'] = i + 1
                progress_var.set(f"已生成 {i + 1}/{count} 个卡密")
                progress_dialog.update()

            progress_dialog.destroy()

            # 保存到文件
            if generated_keys:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"keys_batch_{project_code}_{timestamp}.txt"

                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(f"批量生成卡密 - {datetime.now()}\n")
                    f.write(f"项目代码: {project_code}\n")
                    f.write(f"数量: {len(generated_keys)}\n")
                    f.write(f"有效期: {expire_days}天\n")
                    f.write("-" * 50 + "\n")
                    for key in generated_keys:
                        f.write(f"{key}\n")

                messagebox.showinfo("成功", f"批量生成完成!\n项目: {project_code}\n生成数量: {len(generated_keys)}\n保存文件: {filename}")
                self.refresh_license_data()
            else:
                messagebox.showerror("错误", "批量生成失败")

        # 在新线程中执行生成
        threading.Thread(target=generate_batch, daemon=True).start()
    
    def generate_key_code(self, length=16):
        """生成卡密"""
        chars = string.ascii_uppercase + string.digits
        key_parts = []
        
        for _ in range(4):
            part = ''.join(random.choices(chars, k=4))
            key_parts.append(part)
        
        return '-'.join(key_parts)
    
    def add_license(self, key_code, project_code="DEFAULT", expire_days=30, max_uses=-1):
        """添加卡密到数据库"""
        try:
            expire_date = None
            if expire_days > 0:
                expire_date = (datetime.now() + timedelta(days=expire_days)).isoformat()

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO license_keys (key_code, project_code, expire_date, max_uses)
                VALUES (?, ?, ?, ?)
            ''', (key_code, project_code, expire_date, max_uses))

            conn.commit()
            conn.close()
            return True

        except sqlite3.IntegrityError:
            return False
        except Exception:
            return False
    
    def refresh_license_data(self):
        """刷新卡密数据"""
        # 清空现有数据
        for item in self.license_tree.get_children():
            self.license_tree.delete(item)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT key_code, project_code, hwid, status, expire_date, created_at,
                       last_used, use_count, max_uses
                FROM license_keys
                ORDER BY created_at DESC
                LIMIT 1000
            ''')
            
            results = cursor.fetchall()
            conn.close()
            
            for row in results:
                key_code, project_code, hwid, status, expire_date, created_at, last_used, use_count, max_uses = row

                status_text = "有效" if status == 1 else "禁用"
                hwid_short = (hwid[:16] + "...") if hwid and len(hwid) > 16 else (hwid or "未绑定")
                expire_short = expire_date[:10] if expire_date else "永不过期"
                use_info = f"{use_count}/{max_uses if max_uses > 0 else '∞'}"
                last_used_short = last_used[:10] if last_used else "从未使用"
                created_short = created_at[:10] if created_at else ""

                self.license_tree.insert('', 'end', values=(
                    key_code, project_code or "DEFAULT", status_text, hwid_short, expire_short,
                    use_info, last_used_short, created_short
                ))
                
        except Exception as e:
            messagebox.showerror("错误", f"刷新数据失败: {e}")
    
    def refresh_log_data(self):
        """刷新日志数据"""
        self.log_text.delete(1.0, tk.END)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT key_code, hwid, ip_address, result, error_msg, timestamp
                FROM auth_logs 
                ORDER BY timestamp DESC 
                LIMIT 500
            ''')
            
            results = cursor.fetchall()
            conn.close()
            
            for row in results:
                key_code, hwid, ip_address, result, error_msg, timestamp = row
                
                result_text = "成功" if result == "success" else "失败"
                hwid_short = (hwid[:16] + "...") if hwid and len(hwid) > 16 else (hwid or "N/A")
                error_info = f" - {error_msg}" if error_msg else ""
                
                log_line = f"[{timestamp}] {key_code} | {result_text} | {ip_address} | {hwid_short}{error_info}\n"
                self.log_text.insert(tk.END, log_line)
                
        except Exception as e:
            self.log_text.insert(tk.END, f"刷新日志失败: {e}\n")
    
    def refresh_stats_data(self):
        """刷新统计数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取各种统计数据
            stats = {}
            
            cursor.execute('SELECT COUNT(*) FROM license_keys')
            stats['total_keys'] = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM license_keys WHERE status = 1')
            stats['active_keys'] = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM license_keys WHERE hwid IS NOT NULL')
            stats['bound_keys'] = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM auth_logs')
            stats['total_auths'] = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM auth_logs WHERE result = "success"')
            stats['success_auths'] = cursor.fetchone()[0]
            
            cursor.execute('''
                SELECT COUNT(*) FROM auth_logs 
                WHERE timestamp > datetime('now', '-24 hours')
            ''')
            stats['today_auths'] = cursor.fetchone()[0]
            
            conn.close()
            
            # 计算成功率
            if stats['total_auths'] > 0:
                success_rate = (stats['success_auths'] / stats['total_auths']) * 100
                stats['success_rate'] = f"{success_rate:.1f}%"
            else:
                stats['success_rate'] = "0%"
            
            # 更新显示
            for key, value in stats.items():
                if key in self.stats_labels:
                    self.stats_labels[key].config(text=str(value))
                    
        except Exception as e:
            messagebox.showerror("错误", f"刷新统计失败: {e}")
    
    def refresh_all_data(self):
        """刷新所有数据"""
        self.refresh_license_data()
        self.refresh_log_data()
        self.refresh_stats_data()
    
    def start_auto_refresh(self):
        """启动自动刷新"""
        self.refresh_all_data()
        # 每30秒自动刷新一次
        self.refresh_timer = self.window.after(30000, self.start_auto_refresh)
    
    def enable_selected_license(self):
        """启用选中的卡密"""
        selected = self.license_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择要启用的卡密")
            return
        
        key_code = self.license_tree.item(selected[0])['values'][0]
        if self.update_license_status(key_code, 1):
            messagebox.showinfo("成功", f"卡密已启用: {key_code}")
            self.refresh_license_data()
        else:
            messagebox.showerror("错误", "启用失败")
    
    def disable_selected_license(self):
        """禁用选中的卡密"""
        selected = self.license_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择要禁用的卡密")
            return
        
        key_code = self.license_tree.item(selected[0])['values'][0]
        if messagebox.askyesno("确认", f"确定要禁用卡密: {key_code}?"):
            if self.update_license_status(key_code, 0):
                messagebox.showinfo("成功", f"卡密已禁用: {key_code}")
                self.refresh_license_data()
            else:
                messagebox.showerror("错误", "禁用失败")
    
    def delete_selected_license(self):
        """删除选中的卡密"""
        selected = self.license_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择要删除的卡密")
            return
        
        key_code = self.license_tree.item(selected[0])['values'][0]
        if messagebox.askyesno("确认", f"确定要删除卡密: {key_code}?\n此操作不可恢复!"):
            if self.delete_license(key_code):
                messagebox.showinfo("成功", f"卡密已删除: {key_code}")
                self.refresh_license_data()
            else:
                messagebox.showerror("错误", "删除失败")
    
    def update_license_status(self, key_code, status):
        """更新卡密状态"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE license_keys SET status = ? WHERE key_code = ?
            ''', (status, key_code))
            
            success = cursor.rowcount > 0
            conn.commit()
            conn.close()
            return success
            
        except Exception:
            return False
    
    def delete_license(self, key_code):
        """删除卡密"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('DELETE FROM license_keys WHERE key_code = ?', (key_code,))
            
            success = cursor.rowcount > 0
            conn.commit()
            conn.close()
            return success
            
        except Exception:
            return False

    def reset_license_hwid(self, key_code):
        """重置卡密的硬件绑定"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE license_keys SET hwid = NULL WHERE key_code = ?
            ''', (key_code,))

            success = cursor.rowcount > 0
            conn.commit()
            conn.close()
            return success

        except Exception:
            return False

    def update_license_info(self, key_code, status, expire_date, max_uses):
        """更新卡密信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 处理过期时间
            if expire_date:
                # 验证日期格式
                from datetime import datetime
                try:
                    datetime.strptime(expire_date, "%Y-%m-%d")
                    expire_date = expire_date + "T23:59:59"
                except ValueError:
                    return False
            else:
                expire_date = None

            cursor.execute('''
                UPDATE license_keys
                SET status = ?, expire_date = ?, max_uses = ?
                WHERE key_code = ?
            ''', (status, expire_date, max_uses, key_code))

            success = cursor.rowcount > 0
            conn.commit()
            conn.close()
            return success

        except Exception:
            return False

    def edit_license_dialog(self, event):
        """编辑卡密对话框"""
        selected = self.license_tree.selection()
        if not selected:
            return

        # 获取选中的卡密信息
        values = self.license_tree.item(selected[0])['values']
        key_code = values[0]
        project_code = values[1]
        status_text = values[2]
        hwid = values[3]
        expire_date = values[4]

        # 创建编辑对话框
        dialog = tk.Toplevel(self.window)
        dialog.title(f"编辑卡密 - {key_code}")
        dialog.geometry("500x400")
        dialog.transient(self.window)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (self.window.winfo_rootx() + 50, self.window.winfo_rooty() + 50))

        # 卡密信息（只读）
        info_frame = ttk.LabelFrame(dialog, text="卡密信息", padding="10")
        info_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(info_frame, text=f"卡密: {key_code}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"项目: {project_code}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"HWID: {hwid}").pack(anchor=tk.W)

        # 可编辑字段
        edit_frame = ttk.LabelFrame(dialog, text="可编辑字段", padding="10")
        edit_frame.pack(fill=tk.X, padx=10, pady=5)

        # 状态
        ttk.Label(edit_frame, text="状态:").pack(anchor=tk.W)
        status_var = tk.StringVar(value="1" if status_text == "有效" else "0")
        status_combo = ttk.Combobox(edit_frame, textvariable=status_var, values=["0", "1"], state="readonly")
        status_combo.pack(fill=tk.X, pady=(0, 10))

        # 过期时间
        ttk.Label(edit_frame, text="过期时间 (YYYY-MM-DD 或留空表示永不过期):").pack(anchor=tk.W)
        expire_var = tk.StringVar(value=expire_date if expire_date != "永不过期" else "")
        expire_entry = ttk.Entry(edit_frame, textvariable=expire_var)
        expire_entry.pack(fill=tk.X, pady=(0, 10))

        # 使用次数限制
        ttk.Label(edit_frame, text="使用次数限制 (-1为无限制):").pack(anchor=tk.W)
        uses_var = tk.StringVar(value="-1")
        uses_entry = ttk.Entry(edit_frame, textvariable=uses_var)
        uses_entry.pack(fill=tk.X, pady=(0, 10))

        # 操作按钮
        action_frame = ttk.LabelFrame(dialog, text="操作", padding="10")
        action_frame.pack(fill=tk.X, padx=10, pady=5)

        def reset_hwid():
            if messagebox.askyesno("确认", f"确定要重置卡密 {key_code} 的硬件绑定吗?"):
                if self.reset_license_hwid(key_code):
                    messagebox.showinfo("成功", "硬件绑定已重置")
                    dialog.destroy()
                    self.refresh_license_data()
                else:
                    messagebox.showerror("错误", "重置失败")

        def save_changes():
            try:
                new_status = int(status_var.get())
                new_expire = expire_var.get().strip()
                new_max_uses = int(uses_var.get())

                if self.update_license_info(key_code, new_status, new_expire, new_max_uses):
                    messagebox.showinfo("成功", "卡密信息已更新")
                    dialog.destroy()
                    self.refresh_license_data()
                else:
                    messagebox.showerror("错误", "更新失败")
            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字")

        ttk.Button(action_frame, text="重置硬件绑定", command=reset_hwid).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(action_frame, text="保存修改", command=save_changes).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(action_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT)

    def on_selection_change(self, event):
        """选择变化事件"""
        selected = self.license_tree.selection()
        count = len(selected)
        if count == 0:
            self.selection_label.config(text="未选择")
        elif count == 1:
            self.selection_label.config(text="已选择 1 项")
        else:
            self.selection_label.config(text=f"已选择 {count} 项")

    def select_all_licenses(self):
        """全选卡密"""
        children = self.license_tree.get_children()
        self.license_tree.selection_set(children)

    def invert_selection(self):
        """反选"""
        all_children = self.license_tree.get_children()
        selected = self.license_tree.selection()

        # 计算需要选择的项目（未选择的）
        to_select = [item for item in all_children if item not in selected]

        # 清除当前选择并选择新的项目
        self.license_tree.selection_set(to_select)

    def batch_enable_licenses(self):
        """批量启用卡密"""
        selected = self.license_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择要启用的卡密")
            return

        if messagebox.askyesno("确认", f"确定要启用选中的 {len(selected)} 个卡密吗?"):
            success_count = 0
            for item in selected:
                key_code = self.license_tree.item(item)['values'][0]
                if self.update_license_status(key_code, 1):
                    success_count += 1

            messagebox.showinfo("完成", f"成功启用 {success_count}/{len(selected)} 个卡密")
            self.refresh_license_data()

    def batch_disable_licenses(self):
        """批量禁用卡密"""
        selected = self.license_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择要禁用的卡密")
            return

        if messagebox.askyesno("确认", f"确定要禁用选中的 {len(selected)} 个卡密吗?"):
            success_count = 0
            for item in selected:
                key_code = self.license_tree.item(item)['values'][0]
                if self.update_license_status(key_code, 0):
                    success_count += 1

            messagebox.showinfo("完成", f"成功禁用 {success_count}/{len(selected)} 个卡密")
            self.refresh_license_data()

    def batch_delete_licenses(self):
        """批量删除卡密"""
        selected = self.license_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择要删除的卡密")
            return

        if messagebox.askyesno("确认", f"确定要删除选中的 {len(selected)} 个卡密吗?\n此操作不可恢复!"):
            success_count = 0
            for item in selected:
                key_code = self.license_tree.item(item)['values'][0]
                if self.delete_license(key_code):
                    success_count += 1

            messagebox.showinfo("完成", f"成功删除 {success_count}/{len(selected)} 个卡密")
            self.refresh_license_data()
    
    def clear_logs(self):
        """清空日志"""
        if messagebox.askyesno("确认", "确定要清空所有验证日志吗?\n此操作不可恢复!"):
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute('DELETE FROM auth_logs')
                conn.commit()
                conn.close()
                
                messagebox.showinfo("成功", "日志已清空")
                self.refresh_log_data()
                
            except Exception as e:
                messagebox.showerror("错误", f"清空日志失败: {e}")
    
    def export_licenses(self):
        """导出卡密"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"licenses_export_{timestamp}.txt"
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT key_code, status, expire_date, use_count, max_uses, created_at
                FROM license_keys 
                ORDER BY created_at DESC
            ''')
            
            results = cursor.fetchall()
            conn.close()
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"卡密导出 - {datetime.now()}\n")
                f.write("=" * 60 + "\n")
                f.write(f"{'卡密':<20} {'状态':<6} {'过期时间':<12} {'使用次数':<10} {'创建时间':<12}\n")
                f.write("-" * 60 + "\n")
                
                for row in results:
                    key_code, status, expire_date, use_count, max_uses, created_at = row
                    status_text = "有效" if status == 1 else "禁用"
                    expire_short = expire_date[:10] if expire_date else "永不过期"
                    use_info = f"{use_count}/{max_uses if max_uses > 0 else '∞'}"
                    created_short = created_at[:10] if created_at else ""
                    
                    f.write(f"{key_code:<20} {status_text:<6} {expire_short:<12} {use_info:<10} {created_short:<12}\n")
            
            messagebox.showinfo("成功", f"卡密已导出到: {filename}")
            
        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {e}")
    
    def export_logs(self):
        """导出日志"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"logs_export_{timestamp}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.get(1.0, tk.END))
            
            messagebox.showinfo("成功", f"日志已导出到: {filename}")
            
        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {e}")
    
    def show_about(self):
        """显示关于信息"""
        about_text = """验证服务器管理界面 v1.0

功能特性:
• 卡密添加、删除、启用、禁用
• 批量生成卡密
• 验证日志查看
• 统计信息显示
• 数据导出功能

作者: AI Assistant
技术支持: QQ 2267617536"""
        
        messagebox.showinfo("关于", about_text)
    
    def run(self):
        """运行UI"""
        # 检查数据库
        if not os.path.exists(self.db_path):
            messagebox.showerror("错误", f"数据库文件不存在: {self.db_path}\n请先启动验证服务器")
            return
        
        self.init_window()
        self.window.mainloop()

def main():
    """主函数"""
    app = ServerUIManager()
    app.run()

if __name__ == "__main__":
    main()
