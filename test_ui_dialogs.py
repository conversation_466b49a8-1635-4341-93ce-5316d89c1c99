#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI对话框测试脚本
测试添加卡密和批量生成对话框的按钮显示
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class UIDialogTester:
    """UI对话框测试器"""
    
    def __init__(self):
        self.window = tk.Tk()
        self.window.title("UI对话框测试")
        self.window.geometry("300x200")
        
        # 创建测试按钮
        ttk.Label(self.window, text="UI对话框测试", font=("Arial", 14, "bold")).pack(pady=20)
        
        ttk.Button(self.window, text="测试添加卡密对话框", 
                  command=self.test_add_license_dialog, width=25).pack(pady=10)
        
        ttk.Button(self.window, text="测试批量生成对话框", 
                  command=self.test_batch_generate_dialog, width=25).pack(pady=10)
        
        ttk.Button(self.window, text="退出", 
                  command=self.window.quit, width=25).pack(pady=10)
    
    def test_add_license_dialog(self):
        """测试添加卡密对话框"""
        dialog = tk.Toplevel(self.window)
        dialog.title("添加卡密")
        dialog.geometry("400x300")
        dialog.transient(self.window)
        dialog.grab_set()
        
        # 居中显示
        dialog.geometry("+%d+%d" % (self.window.winfo_rootx() + 50, self.window.winfo_rooty() + 50))
        
        # 表单
        ttk.Label(dialog, text="项目代码:").pack(pady=5)
        project_var = tk.StringVar(value="DEFAULT")
        project_entry = ttk.Entry(dialog, textvariable=project_var, width=40)
        project_entry.pack(pady=5)
        
        ttk.Label(dialog, text="卡密 (留空自动生成):").pack(pady=5)
        key_entry = ttk.Entry(dialog, width=40)
        key_entry.pack(pady=5)
        
        ttk.Label(dialog, text="有效期 (天数):").pack(pady=5)
        expire_var = tk.StringVar(value="30")
        expire_entry = ttk.Entry(dialog, textvariable=expire_var, width=20)
        expire_entry.pack(pady=5)
        
        ttk.Label(dialog, text="使用次数限制 (-1为无限制):").pack(pady=5)
        uses_var = tk.StringVar(value="-1")
        uses_entry = ttk.Entry(dialog, textvariable=uses_var, width=20)
        uses_entry.pack(pady=5)
        
        def add_license():
            project_code = project_var.get().strip() or "DEFAULT"
            key_code = key_entry.get().strip() or "TEST-XXXX-XXXX-XXXX"
            expire_days = expire_var.get()
            max_uses = uses_var.get()
            
            messagebox.showinfo("测试成功", f"添加卡密测试\n项目: {project_code}\n卡密: {key_code}\n有效期: {expire_days}天\n使用次数: {max_uses}")
            dialog.destroy()
        
        # 按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)
        
        ttk.Button(button_frame, text="确认", command=add_license, width=12).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=dialog.destroy, width=12).pack(side=tk.LEFT, padx=5)
    
    def test_batch_generate_dialog(self):
        """测试批量生成对话框"""
        dialog = tk.Toplevel(self.window)
        dialog.title("批量生成卡密")
        dialog.geometry("400x250")
        dialog.transient(self.window)
        dialog.grab_set()
        
        # 居中显示
        dialog.geometry("+%d+%d" % (self.window.winfo_rootx() + 50, self.window.winfo_rooty() + 50))
        
        # 表单
        ttk.Label(dialog, text="项目代码:").pack(pady=5)
        project_var = tk.StringVar(value="DEFAULT")
        project_entry = ttk.Entry(dialog, textvariable=project_var, width=40)
        project_entry.pack(pady=5)
        
        ttk.Label(dialog, text="生成数量:").pack(pady=5)
        count_var = tk.StringVar(value="10")
        count_entry = ttk.Entry(dialog, textvariable=count_var, width=20)
        count_entry.pack(pady=5)
        
        ttk.Label(dialog, text="有效期 (天数):").pack(pady=5)
        expire_var = tk.StringVar(value="30")
        expire_entry = ttk.Entry(dialog, textvariable=expire_var, width=20)
        expire_entry.pack(pady=5)
        
        def start_batch_generate():
            try:
                project_code = project_var.get().strip() or "DEFAULT"
                count = int(count_var.get())
                expire_days = int(expire_var.get())
            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字")
                return
            
            if count < 1 or count > 1000:
                messagebox.showerror("错误", "生成数量必须在1-1000之间")
                return
            
            messagebox.showinfo("测试成功", f"批量生成测试\n项目: {project_code}\n数量: {count}\n有效期: {expire_days}天")
            dialog.destroy()
        
        # 按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)
        
        ttk.Button(button_frame, text="确认", command=start_batch_generate, width=12).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=dialog.destroy, width=12).pack(side=tk.LEFT, padx=5)
    
    def run(self):
        """运行测试"""
        self.window.mainloop()

def main():
    """主函数"""
    print("启动UI对话框测试...")
    tester = UIDialogTester()
    tester.run()

if __name__ == "__main__":
    main()
