# Python加密验证工具套件 - 使用指南

## 🚀 快速开始

### 1. 验证服务器
```bash
# 启动验证服务器
python auth_server.py

# 使用图形界面管理卡密（推荐）
python server_ui.py

# 或使用命令行管理
python demo_server_manager.py
```

### 2. 运行程序
```bash
# 直接运行
python 涨停解读-v5.0.py

# 或运行已打包的exe文件
release/涨停解读工具_v5.0_精简版.exe
```

### 3. 打包为exe（已完成）
已生成优化版exe文件，体积仅28.3MB：
- `release/涨停解读工具_v5.0_精简版.exe`

## 📁 文件说明

### 核心文件
- `涨停解读-v5.0.py` - 主程序（已集成验证）
- `GetDayZhangTing.py` - API接口模块
- `auth_client.py` - 客户端验证模块
- `auth_server.py` - 验证服务器

### 管理工具
- `server_ui.py` - 服务器图形界面管理工具（推荐）
- `demo_server_manager.py` - 服务器命令行管理工具
- `anti_debug.py` - 反调试模块

### 发布文件
- `release/涨停解读工具_v5.0_精简版.exe` - 优化版exe文件（28.3MB）

## 🔧 验证系统使用

### 服务器端
1. 启动验证服务器：`python auth_server.py`
2. 添加卡密：`python demo_server_manager.py` 选择选项1
3. 服务器默认在 `http://localhost:5000` 运行

### 客户端
1. 运行程序时会自动请求输入卡密
2. 首次使用会绑定硬件ID
3. 验证成功后程序正常运行

## 🛠️ 常见问题

### 验证失败
- 确保验证服务器正在运行
- 检查卡密是否正确
- 检查是否已绑定其他设备

### 打包问题
- 中文文件名可能导致打包失败
- 请参考手动打包指南

## 📞 技术支持

如有问题，请联系：
- QQ: 2267617536

## 📝 注意事项

1. 请勿修改验证模块
2. 请勿尝试绕过验证
3. 请在使用前修改默认密钥
