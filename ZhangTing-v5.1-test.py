#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 设置编码环境变量，确保在Windows下正常运行
import os
import sys

# 设置环境变量
os.environ['PYTHONIOENCODING'] = 'utf-8'

print("开始加载验证模块...")

# 验证模块导入
AUTH_ENABLED = True
try:
    from auth_client import AuthClient
    from anti_debug import AntiDebug, check_debugger, check_vm
    print("✓ 验证模块加载成功")
except ImportError as e:
    print(f"⚠ 验证模块加载失败: {e}")
    AUTH_ENABLED = False

# 验证配置
AUTH_CONFIG = {
    'SERVER_URL': 'http://113.44.172.184:16888',  # 验证服务器地址
    'SECRET_KEY': 'your-secret-key-change-this',  # 与服务器保持一致
    'PROJECT_CODE': 'ZHANGTING_V51',  # 项目代码
    'PROJECT_NAME': '异动解读工具v5.1',  # 项目名称
    'DEMO_MODE': not AUTH_ENABLED,  # 演示模式（无验证模块时启用）
    'TEST_MODE': True,  # 测试模式（跳过虚拟机检测）
}

def safe_print(text):
    """安全的打印函数，处理编码问题"""
    try:
        print(text)
    except (UnicodeEncodeError, UnicodeDecodeError, AttributeError):
        try:
            # 移除emoji字符，只保留基本文本
            import re
            clean_text = re.sub(r'[^\x00-\x7F]+', '', str(text))
            print(clean_text)
        except:
            # 最后的备用方案：只打印ASCII字符
            try:
                ascii_text = str(text).encode('ascii', 'ignore').decode('ascii')
                print(ascii_text)
            except:
                # 如果所有方法都失败，静默处理
                pass

def perform_license_validation():
    """执行卡密验证"""
    if AUTH_CONFIG['DEMO_MODE']:
        safe_print("演示模式：跳过验证")
        return True
    
    try:
        # 反调试检测
        if check_debugger():
            safe_print("检测到调试器，程序退出")
            sys.exit(1)
        
        # 虚拟机检测（测试模式下跳过）
        if not AUTH_CONFIG.get('TEST_MODE', False) and check_vm():
            safe_print("检测到虚拟机环境，程序退出")
            sys.exit(1)
        elif AUTH_CONFIG.get('TEST_MODE', False):
            safe_print("测试模式：跳过虚拟机检测")
        
        # 创建验证客户端
        auth_client = AuthClient(
            AUTH_CONFIG['SERVER_URL'], 
            AUTH_CONFIG['SECRET_KEY'],
            AUTH_CONFIG['PROJECT_CODE']
        )
        
        safe_print(f"硬件ID: {auth_client.get_hwid()}")
        
        # 尝试使用保存的卡密
        success, message = auth_client.validate_license()
        
        if success:
            safe_print(f"使用保存的卡密验证成功: {message}")
            return True
        
        # 如果保存的卡密无效，显示输入对话框
        try:
            from license_dialog import show_license_dialog
            
            key_code, auto_save = show_license_dialog(
                project_name=AUTH_CONFIG['PROJECT_NAME'],
                auto_save_enabled=True
            )
            
            if not key_code:
                safe_print("用户取消验证，程序退出")
                return False
            
            # 执行验证
            safe_print("正在验证卡密...")
            success, message = auth_client.validate_license(key_code, auto_save)
            
            if success:
                safe_print(f"验证成功: {message}")
                if auto_save:
                    safe_print("卡密已保存，下次启动将自动验证")
                return True
            else:
                safe_print(f"验证失败: {message}")
                return False
                
        except ImportError:
            # 如果没有license_dialog模块，使用简单输入
            import tkinter as tk
            from tkinter import simpledialog, messagebox
            
            root = tk.Tk()
            root.withdraw()
            
            key_code = simpledialog.askstring(
                "卡密验证", 
                f"{AUTH_CONFIG['PROJECT_NAME']}\n\n请输入您的卡密:\n\n购买卡密联系QQ：**********",
                show='*'
            )
            
            if not key_code:
                messagebox.showerror("错误", "未输入卡密，程序退出")
                root.destroy()
                return False
            
            success, message = auth_client.validate_license(key_code, True)
            root.destroy()
            
            if success:
                safe_print(f"验证成功: {message}")
                return True
            else:
                safe_print(f"验证失败: {message}")
                return False
            
    except Exception as e:
        safe_print(f"验证异常: {e}")
        return False

def main():
    """主函数"""
    try:
        print("启动异动解读工具 v5.1 (测试版)...")
        print("=" * 60)
    except UnicodeEncodeError:
        print("Starting ZhangTing Tool v5.1...")
        print("=" * 60)

    # 执行卡密验证
    if not perform_license_validation():
        safe_print("验证失败，程序退出")
        return

    safe_print("\n验证通过，程序可以正常运行!")
    safe_print("这里应该启动主程序...")

if __name__ == "__main__":
    main()
