#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证系统测试脚本
"""

print("开始测试验证系统...")

# 测试导入
try:
    from auth_client import AuthClient
    print("✓ AuthClient 导入成功")
except ImportError as e:
    print(f"✗ AuthClient 导入失败: {e}")

try:
    from anti_debug import AntiDebug, check_debugger, check_vm
    print("✓ AntiDebug 导入成功")
except ImportError as e:
    print(f"✗ AntiDebug 导入失败: {e}")

try:
    from license_dialog import show_license_dialog
    print("✓ license_dialog 导入成功")
except ImportError as e:
    print(f"✗ license_dialog 导入失败: {e}")

# 测试反调试功能
print("\n测试反调试功能...")
try:
    result = check_debugger()
    print(f"调试器检测: {result}")
except Exception as e:
    print(f"调试器检测失败: {e}")

try:
    result = check_vm()
    print(f"虚拟机检测: {result}")
except Exception as e:
    print(f"虚拟机检测失败: {e}")

# 测试验证客户端
print("\n测试验证客户端...")
try:
    client = AuthClient(
        "http://113.44.172.184:16888",
        "your-secret-key-change-this",
        "TEST_PROJECT"
    )
    print(f"✓ 验证客户端创建成功")
    print(f"硬件ID: {client.get_hwid()}")
except Exception as e:
    print(f"✗ 验证客户端创建失败: {e}")

print("\n测试完成!")
