#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI修复验证脚本
验证server_ui.py中的对话框按钮是否正常显示
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

def test_add_license_dialog():
    """测试添加卡密对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    dialog = tk.Toplevel(root)
    dialog.title("添加卡密")
    dialog.geometry("400x300")
    dialog.grab_set()
    
    # 表单
    ttk.Label(dialog, text="项目代码:").pack(pady=5)
    project_var = tk.StringVar(value="DEFAULT")
    project_entry = ttk.Entry(dialog, textvariable=project_var, width=40)
    project_entry.pack(pady=5)
    
    ttk.Label(dialog, text="卡密 (留空自动生成):").pack(pady=5)
    key_entry = ttk.Entry(dialog, width=40)
    key_entry.pack(pady=5)
    
    ttk.Label(dialog, text="有效期 (天数):").pack(pady=5)
    expire_var = tk.StringVar(value="30")
    expire_entry = ttk.Entry(dialog, textvariable=expire_var, width=20)
    expire_entry.pack(pady=5)
    
    ttk.Label(dialog, text="使用次数限制 (-1为无限制):").pack(pady=5)
    uses_var = tk.StringVar(value="-1")
    uses_entry = ttk.Entry(dialog, textvariable=uses_var, width=20)
    uses_entry.pack(pady=5)
    
    def add_license():
        messagebox.showinfo("测试", "添加卡密按钮工作正常!")
        dialog.destroy()
        root.quit()
    
    # 按钮框架
    button_frame = ttk.Frame(dialog)
    button_frame.pack(pady=20)
    
    ttk.Button(button_frame, text="确认", command=add_license, width=12).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="取消", command=lambda: (dialog.destroy(), root.quit()), width=12).pack(side=tk.LEFT, padx=5)
    
    print("✓ 添加卡密对话框已创建，请检查按钮是否正常显示")
    root.mainloop()

def test_batch_generate_dialog():
    """测试批量生成对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    dialog = tk.Toplevel(root)
    dialog.title("批量生成卡密")
    dialog.geometry("400x250")
    dialog.grab_set()
    
    # 表单
    ttk.Label(dialog, text="项目代码:").pack(pady=5)
    project_var = tk.StringVar(value="DEFAULT")
    project_entry = ttk.Entry(dialog, textvariable=project_var, width=40)
    project_entry.pack(pady=5)
    
    ttk.Label(dialog, text="生成数量:").pack(pady=5)
    count_var = tk.StringVar(value="10")
    count_entry = ttk.Entry(dialog, textvariable=count_var, width=20)
    count_entry.pack(pady=5)
    
    ttk.Label(dialog, text="有效期 (天数):").pack(pady=5)
    expire_var = tk.StringVar(value="30")
    expire_entry = ttk.Entry(dialog, textvariable=expire_var, width=20)
    expire_entry.pack(pady=5)
    
    def start_batch_generate():
        messagebox.showinfo("测试", "批量生成按钮工作正常!")
        dialog.destroy()
        root.quit()
    
    # 按钮框架
    button_frame = ttk.Frame(dialog)
    button_frame.pack(pady=20)
    
    ttk.Button(button_frame, text="确认", command=start_batch_generate, width=12).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="取消", command=lambda: (dialog.destroy(), root.quit()), width=12).pack(side=tk.LEFT, padx=5)
    
    print("✓ 批量生成对话框已创建，请检查按钮是否正常显示")
    root.mainloop()

def main():
    """主函数"""
    print("UI修复验证脚本")
    print("=" * 40)
    
    choice = input("选择测试:\n1. 添加卡密对话框\n2. 批量生成对话框\n3. 退出\n请输入选择 (1-3): ")
    
    if choice == "1":
        print("\n测试添加卡密对话框...")
        test_add_license_dialog()
        print("✓ 添加卡密对话框测试完成")
    elif choice == "2":
        print("\n测试批量生成对话框...")
        test_batch_generate_dialog()
        print("✓ 批量生成对话框测试完成")
    elif choice == "3":
        print("退出测试")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
