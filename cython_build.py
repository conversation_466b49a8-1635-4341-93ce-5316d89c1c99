#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cython编译脚本
将关键模块编译为C扩展以提高安全性

功能特性:
- 编译验证模块
- 编译反调试模块
- 自动处理文件重命名
- 清理临时文件

作者: 股中掘金
版本: 1.0
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
from distutils.core import setup
from distutils.extension import Extension

# 需要编译的模块
COMPILE_MODULES = {
    'auth_client.py': 'auth_core',
    'anti_debug.py': 'security_core',
    'license_dialog.py': 'ui_core'
}

class CythonBuilder:
    """Cython构建器"""
    
    def __init__(self):
        self.project_dir = Path.cwd()
        self.build_dir = self.project_dir / 'cython_build'
        self.backup_dir = self.project_dir / 'backup_original'
        
    def check_cython(self):
        """检查Cython是否安装"""
        try:
            import Cython
            print(f"✓ Cython版本: {Cython.__version__}")
            return True
        except ImportError:
            print("✗ 未安装Cython")
            print("请运行: pip install Cython")
            return False
    
    def create_backup(self):
        """创建原始文件备份"""
        print("创建原始文件备份...")
        
        self.backup_dir.mkdir(exist_ok=True)
        
        for original_file in COMPILE_MODULES.keys():
            src = self.project_dir / original_file
            if src.exists():
                dst = self.backup_dir / original_file
                shutil.copy2(src, dst)
                print(f"  备份: {original_file}")
        
        print("✓ 备份完成")
    
    def prepare_build_dir(self):
        """准备构建目录"""
        print("准备构建目录...")
        
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
        self.build_dir.mkdir()
        
        # 复制源文件到构建目录
        for original_file, module_name in COMPILE_MODULES.items():
            src = self.project_dir / original_file
            if src.exists():
                dst = self.build_dir / f"{module_name}.py"
                shutil.copy2(src, dst)
                print(f"  复制: {original_file} -> {module_name}.py")
        
        print("✓ 构建目录准备完成")
    
    def create_setup_script(self):
        """创建setup脚本"""
        setup_content = '''
from distutils.core import setup
from Cython.Build import cythonize
from distutils.extension import Extension

# 编译选项
compiler_directives = {
    'language_level': 3,
    'boundscheck': False,
    'wraparound': False,
    'initializedcheck': False,
    'cdivision': True,
    'embedsignature': False,
    'optimize.use_switch': True,
    'optimize.unpack_method_calls': True,
}

# 要编译的模块
modules = [
    "auth_core.py",
    "security_core.py", 
    "ui_core.py"
]

# 执行编译
setup(
    ext_modules=cythonize(
        modules,
        compiler_directives=compiler_directives,
        build_dir="build"
    ),
    zip_safe=False,
)
'''
        
        setup_file = self.build_dir / 'setup.py'
        with open(setup_file, 'w', encoding='utf-8') as f:
            f.write(setup_content)
        
        return setup_file
    
    def compile_modules(self):
        """编译模块"""
        print("开始Cython编译...")
        
        setup_file = self.create_setup_script()
        
        # 执行编译
        cmd = [sys.executable, str(setup_file), 'build_ext', '--inplace']
        
        try:
            result = subprocess.run(
                cmd,
                cwd=self.build_dir,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            if result.returncode == 0:
                print("✓ Cython编译成功")
                return True
            else:
                print("✗ Cython编译失败:")
                print(result.stderr)
                return False
                
        except subprocess.TimeoutExpired:
            print("✗ 编译超时")
            return False
        except Exception as e:
            print(f"✗ 编译异常: {e}")
            return False
    
    def copy_compiled_modules(self):
        """复制编译后的模块"""
        print("复制编译后的模块...")

        # 查找编译后的文件
        for pattern in ['*.pyd', '*.so']:
            for compiled_file in self.build_dir.glob(pattern):
                # 确定目标文件名
                original_name = None
                for orig, module in COMPILE_MODULES.items():
                    if module in compiled_file.name:
                        # 保持原始扩展名，只是重命名为编译后的文件
                        original_name = f"{orig.replace('.py', '')}_compiled{compiled_file.suffix}"
                        break

                if original_name:
                    dst = self.project_dir / original_name
                    shutil.copy2(compiled_file, dst)
                    print(f"  复制: {compiled_file.name} -> {original_name}")

        print("✓ 编译模块复制完成")

    def create_import_wrappers(self):
        """创建导入包装器，保持原始文件名"""
        print("创建导入包装器...")

        for original_file, module_name in COMPILE_MODULES.items():
            # 查找对应的编译文件
            compiled_file = None
            for pattern in ['*.pyd', '*.so']:
                for cf in self.project_dir.glob(f"{original_file.replace('.py', '')}_compiled{pattern[1:]}"):
                    compiled_file = cf.name.replace(cf.suffix, '')
                    break
                if compiled_file:
                    break

            if compiled_file:
                # 创建包装器文件
                wrapper_content = f'''# -*- coding: utf-8 -*-
"""
{original_file} 的导入包装器
自动导入编译后的模块
"""

try:
    # 导入编译后的模块
    from {compiled_file} import *
except ImportError as e:
    print(f"警告: 无法导入编译模块 {compiled_file}: {{e}}")
    # 如果编译模块导入失败，这里可以添加备用方案
    raise ImportError(f"编译模块 {compiled_file} 不可用")
'''

                wrapper_file = self.project_dir / original_file
                with open(wrapper_file, 'w', encoding='utf-8') as f:
                    f.write(wrapper_content)

                print(f"  创建包装器: {original_file} -> {compiled_file}")

        print("✓ 导入包装器创建完成")
    
    def cleanup(self):
        """清理临时文件"""
        print("清理临时文件...")
        
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
            print("✓ 构建目录已清理")
    
    def restore_from_backup(self):
        """从备份恢复文件"""
        print("从备份恢复文件...")
        
        if not self.backup_dir.exists():
            print("✗ 备份目录不存在")
            return False
        
        for original_file in COMPILE_MODULES.keys():
            backup_file = self.backup_dir / original_file
            if backup_file.exists():
                dst = self.project_dir / original_file
                shutil.copy2(backup_file, dst)
                print(f"  恢复: {original_file}")
        
        print("✓ 文件恢复完成")
        return True
    
    def build(self):
        """执行完整构建"""
        print("Cython模块编译工具")
        print("=" * 50)
        
        # 检查Cython
        if not self.check_cython():
            return False
        
        try:
            # 1. 创建备份
            self.create_backup()
            
            # 2. 准备构建目录
            self.prepare_build_dir()
            
            # 3. 编译模块
            if not self.compile_modules():
                print("编译失败，恢复原始文件")
                self.restore_from_backup()
                return False
            
            # 4. 复制编译后的模块
            self.copy_compiled_modules()

            # 5. 创建导入包装器（保持原始文件名）
            self.create_import_wrappers()

            # 6. 清理临时文件
            self.cleanup()
            
            print("\n" + "=" * 50)
            print("🎉 Cython编译完成!")
            print("关键模块已编译为C扩展，安全性大幅提升")
            print("=" * 50)
            
            return True
            
        except Exception as e:
            print(f"\n编译异常: {e}")
            print("恢复原始文件...")
            self.restore_from_backup()
            self.cleanup()
            return False

def main():
    """主函数"""
    builder = CythonBuilder()
    
    # 检查必要文件
    missing_files = []
    for file_name in COMPILE_MODULES.keys():
        if not Path(file_name).exists():
            missing_files.append(file_name)
    
    if missing_files:
        print("错误：缺少必要文件:")
        for file_name in missing_files:
            print(f"  - {file_name}")
        return False
    
    try:
        success = builder.build()
        return success
    except KeyboardInterrupt:
        print("\n用户中断编译")
        builder.restore_from_backup()
        builder.cleanup()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ 编译成功!")
        print("现在可以使用PyInstaller打包程序")
    else:
        print("\n❌ 编译失败")
    
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
