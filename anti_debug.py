#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级反调试模块
提供多层次的反调试和反分析保护

功能特性:
- 调试器检测
- 虚拟机检测
- 进程监控
- 时间检测
- 内存保护
- 代码完整性检查

作者: 股中掘金
版本: 1.0
"""

import os
import sys
import time
import ctypes
import platform
import threading
import hashlib
import psutil
from ctypes import wintypes

class AdvancedAntiDebug:
    """高级反调试类"""
    
    def __init__(self):
        self.is_windows = platform.system() == 'Windows'
        self.start_time = time.time()
        self.check_interval = 5  # 检查间隔(秒)
        self.monitoring = False
        self.monitor_thread = None
        
        if self.is_windows:
            self.kernel32 = ctypes.windll.kernel32
            self.ntdll = ctypes.windll.ntdll
    
    def check_debugger_present(self):
        """检查调试器是否存在"""
        if not self.is_windows:
            return False
        
        try:
            # IsDebuggerPresent API
            if self.kernel32.IsDebuggerPresent():
                return True
            
            # CheckRemoteDebuggerPresent API
            debug_flag = ctypes.c_bool()
            if self.kernel32.CheckRemoteDebuggerPresent(
                self.kernel32.GetCurrentProcess(),
                ctypes.byref(debug_flag)
            ):
                if debug_flag.value:
                    return True
            
            # PEB检查
            if self._check_peb_debug_flags():
                return True
                
            return False
        except:
            return False
    
    def _check_peb_debug_flags(self):
        """检查PEB中的调试标志"""
        try:
            # 获取PEB地址
            peb_addr = self._get_peb_address()
            if not peb_addr:
                return False
            
            # 读取BeingDebugged标志
            being_debugged = ctypes.c_ubyte()
            if self.kernel32.ReadProcessMemory(
                self.kernel32.GetCurrentProcess(),
                peb_addr + 2,  # BeingDebugged偏移
                ctypes.byref(being_debugged),
                1,
                None
            ):
                if being_debugged.value:
                    return True
            
            # 读取NtGlobalFlag
            nt_global_flag = ctypes.c_ulong()
            if self.kernel32.ReadProcessMemory(
                self.kernel32.GetCurrentProcess(),
                peb_addr + 0x68,  # NtGlobalFlag偏移
                ctypes.byref(nt_global_flag),
                4,
                None
            ):
                # 检查调试标志
                if nt_global_flag.value & 0x70:
                    return True
            
            return False
        except:
            return False
    
    def _get_peb_address(self):
        """获取PEB地址"""
        try:
            # 通过NtQueryInformationProcess获取PEB地址
            process_info = ctypes.c_ulonglong()
            if self.ntdll.NtQueryInformationProcess(
                self.kernel32.GetCurrentProcess(),
                0,  # ProcessBasicInformation
                ctypes.byref(process_info),
                ctypes.sizeof(process_info),
                None
            ) == 0:
                return process_info.value
            return None
        except:
            return None
    
    def check_debug_processes(self):
        """检查调试器进程"""
        debug_processes = [
            'ollydbg.exe', 'x64dbg.exe', 'x32dbg.exe', 'windbg.exe',
            'ida.exe', 'ida64.exe', 'idaq.exe', 'idaq64.exe',
            'cheatengine.exe', 'processhacker.exe', 'procmon.exe',
            'fiddler.exe', 'wireshark.exe', 'tcpview.exe',
            'regmon.exe', 'filemon.exe', 'procexp.exe'
        ]
        
        try:
            running_processes = [p.name().lower() for p in psutil.process_iter()]
            for debug_proc in debug_processes:
                if debug_proc.lower() in running_processes:
                    return True, debug_proc
            return False, None
        except:
            return False, None
    
    def check_vm_environment(self):
        """检查虚拟机环境"""
        vm_indicators = []
        
        try:
            # 检查系统信息
            system_info = platform.platform().lower()
            vm_keywords = ['vmware', 'virtualbox', 'vbox', 'qemu', 'xen', 'hyper-v']
            for keyword in vm_keywords:
                if keyword in system_info:
                    vm_indicators.append(f"系统信息包含: {keyword}")
            
            # 检查MAC地址
            import uuid
            mac = uuid.getnode()
            mac_str = ':'.join(('%012X' % mac)[i:i+2] for i in range(0, 12, 2))
            
            vm_mac_prefixes = [
                '00:05:69', '00:0C:29', '00:1C:14', '00:50:56',  # VMware
                '08:00:27', '52:54:00',  # VirtualBox
                '00:16:3E', '00:03:FF'   # Xen
            ]
            
            for prefix in vm_mac_prefixes:
                if mac_str.startswith(prefix):
                    vm_indicators.append(f"VM MAC地址: {mac_str}")
            
            # Windows特定检查
            if self.is_windows:
                vm_indicators.extend(self._check_windows_vm())
            
            return len(vm_indicators) > 0, vm_indicators
        except:
            return False, []
    
    def _check_windows_vm(self):
        """Windows虚拟机检查"""
        indicators = []
        
        try:
            import wmi
            c = wmi.WMI()
            
            # 检查计算机型号
            for system in c.Win32_ComputerSystem():
                model = system.Model.lower()
                if any(vm in model for vm in ['vmware', 'virtualbox', 'virtual']):
                    indicators.append(f"计算机型号: {system.Model}")
            
            # 检查BIOS
            for bios in c.Win32_BIOS():
                version = bios.Version.lower()
                if any(vm in version for vm in ['vmware', 'vbox', 'virtual']):
                    indicators.append(f"BIOS版本: {bios.Version}")
            
            # 检查显卡
            for gpu in c.Win32_VideoController():
                if gpu.Name:
                    name = gpu.Name.lower()
                    if any(vm in name for vm in ['vmware', 'virtualbox', 'virtual']):
                        indicators.append(f"显卡: {gpu.Name}")
        except:
            pass
        
        return indicators
    
    def check_timing_attack(self):
        """检查时间攻击"""
        try:
            # RDTSC指令检测
            start = time.perf_counter()
            
            # 执行一些简单操作
            for i in range(1000):
                pass
            
            end = time.perf_counter()
            elapsed = end - start
            
            # 如果执行时间异常长，可能在调试器中
            if elapsed > 0.01:  # 10ms阈值
                return True, f"执行时间异常: {elapsed:.6f}s"
            
            return False, None
        except:
            return False, None
    
    def check_code_integrity(self):
        """检查代码完整性"""
        try:
            # 计算当前模块的哈希
            current_file = __file__
            if current_file.endswith('.pyc'):
                return True, None  # 编译后的文件跳过检查
            
            with open(current_file, 'rb') as f:
                content = f.read()
                current_hash = hashlib.sha256(content).hexdigest()
            
            # 这里应该与预期的哈希值比较
            # 为了演示，我们跳过实际比较
            return False, None
        except:
            return False, None
    
    def comprehensive_check(self):
        """综合检查"""
        threats = []
        
        # 调试器检查
        if self.check_debugger_present():
            threats.append("检测到调试器")
        
        # 进程检查
        has_debug_proc, proc_name = self.check_debug_processes()
        if has_debug_proc:
            threats.append(f"检测到调试进程: {proc_name}")
        
        # 虚拟机检查
        is_vm, vm_indicators = self.check_vm_environment()
        if is_vm:
            threats.append(f"检测到虚拟机环境: {', '.join(vm_indicators[:2])}")
        
        # 时间检查
        is_timing_attack, timing_info = self.check_timing_attack()
        if is_timing_attack:
            threats.append(f"检测到时间异常: {timing_info}")
        
        # 代码完整性检查
        is_modified, modify_info = self.check_code_integrity()
        if is_modified:
            threats.append(f"代码被修改: {modify_info}")
        
        return threats
    
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            threats = self.comprehensive_check()
            if threats:
                print(f"安全威胁检测: {', '.join(threats)}")
                # 这里可以添加更严厉的反制措施
                os._exit(1)
            
            time.sleep(self.check_interval)

# 便捷函数
def quick_anti_debug_check():
    """快速反调试检查"""
    anti_debug = AdvancedAntiDebug()
    threats = anti_debug.comprehensive_check()
    
    if threats:
        print(f"检测到安全威胁: {', '.join(threats)}")
        print("程序将退出")
        sys.exit(1)
    
    return True

def start_anti_debug_monitoring():
    """启动反调试监控"""
    anti_debug = AdvancedAntiDebug()
    anti_debug.start_monitoring()
    return anti_debug

# 示例用法
if __name__ == "__main__":
    print("高级反调试测试")
    print("=" * 30)
    
    anti_debug = AdvancedAntiDebug()
    threats = anti_debug.comprehensive_check()
    
    if threats:
        print("检测到威胁:")
        for threat in threats:
            print(f"  - {threat}")
    else:
        print("环境安全")
    
    # 启动监控
    print("\n启动持续监控...")
    anti_debug.start_monitoring()
    
    try:
        time.sleep(30)  # 监控30秒
    except KeyboardInterrupt:
        pass
    
    anti_debug.stop_monitoring()
    print("监控结束")
